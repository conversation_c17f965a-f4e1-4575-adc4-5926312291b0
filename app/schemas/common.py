"""
Common Pydantic schemas and base classes.
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, ConfigDict, Field

DataType = TypeVar("DataType")


class BaseSchema(BaseModel):
    """Base schema with common configuration."""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
    )


class TimestampMixin(BaseModel):
    """Mixin for models with timestamp fields."""
    
    created_at: datetime
    updated_at: datetime


class IDMixin(BaseModel):
    """Mixin for models with ID field."""
    
    id: int


class PaginationParams(BaseModel):
    """Pagination parameters for list endpoints."""
    
    skip: int = 0
    limit: int = 100
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "skip": 0,
                "limit": 100
            }
        }
    )


class PaginatedResponse(BaseSchema, Generic[DataType]):
    """Generic paginated response schema."""
    
    items: List[DataType]
    total: int
    skip: int
    limit: int
    has_next: bool
    has_prev: bool
    
    @classmethod
    def create(
        cls,
        items: List[DataType],
        total: int,
        skip: int = 0,
        limit: int = 100,
    ) -> "PaginatedResponse[DataType]":
        """Create paginated response."""
        return cls(
            items=items,
            total=total,
            skip=skip,
            limit=limit,
            has_next=skip + limit < total,
            has_prev=skip > 0,
        )


class SuccessResponse(BaseSchema):
    """Generic success response schema."""
    
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseSchema):
    """Generic error response schema."""

    success: bool = False
    error: str
    detail: Optional[str] = None
    errors: Optional[List[Dict[str, Any]]] = None


class BaseResponse(BaseSchema, Generic[DataType]):
    """
    Standardized base response class for all API endpoints with conditional structure.

    This class enforces a consistent response structure across the entire API
    with proper type hints and automatic validation through Pydantic.

    CONDITIONAL RESPONSE STRUCTURE:

    Success responses (status=True):
        - status: True
        - message: Human-readable success message
        - data: Optional response data

    Error responses (status=False):
        - status: False
        - error: Human-readable error message
        - error_code: Standardized error code

    BACKWARD COMPATIBILITY:
    All existing BaseResponse.success() and BaseResponse.error() calls continue to work
    without modification. Error codes are automatically inferred from error messages
    and data context when not explicitly provided.

    STANDARDIZED ERROR CODES:
    - "VALIDATION_ERROR": Input validation failures (422)
    - "NOT_FOUND": Resource not found errors (404)
    - "AUTHENTICATION_ERROR": Authentication failures (401)
    - "AUTHORIZATION_ERROR": Permission/access errors (403)
    - "CONFLICT_ERROR": Resource conflicts (409)
    - "HTTP_ERROR": Default fallback for generic errors (500)

    Examples:
        Success response:
        >>> BaseResponse.success("User created successfully", user_object)

        Error response (new style):
        >>> BaseResponse.error("Authentication failed", "AUTHENTICATION_ERROR")

        Error response (backward compatible):
        >>> BaseResponse.error("User not found")  # auto-infers "NOT_FOUND"
    """

    status: bool = Field(..., description="Boolean indicating success (True) or failure (False)")

    # Success fields
    message: Optional[str] = Field(None, description="Success message (success responses only)")
    data: Optional[DataType] = Field(None, description="Response data (success responses only)")

    # Error fields
    error_message: Optional[str] = Field(None, alias="error", description="Error message (error responses only)")
    error_code: Optional[str] = Field(None, description="Error code (error responses only)")

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """Override model_dump to exclude irrelevant fields based on status.

        Note: kwargs parameter maintained for Pydantic BaseModel interface compatibility.
        """
        if self.status:
            # Success response: only include status, message, and data
            result = {
                "status": self.status,
                "message": self.message,
            }
            # Only include data if it's not None
            if self.data is not None:
                result["data"] = self.data
            else:
                result["data"] = None  # Keep data field even if None for success responses
        else:
            # Error response: only include status, error, and error_code
            result = {
                "status": self.status,
                "error": self.error_message,  # Use the actual field name
                "error_code": self.error_code,
            }

        return result

    model_config = ConfigDict(
        json_schema_extra={
            "examples": [
                {
                    "status": True,
                    "message": "Operation successful",
                    "data": {"result": "example"}
                },
                {
                    "status": False,
                    "error": "Operation failed",
                    "error_code": "VALIDATION_ERROR"
                }
            ]
        }
    )

    @classmethod
    def success(
        cls,
        message: str,
        data: Optional[DataType] = None
    ) -> "BaseResponse[DataType]":
        """
        Create a success response.

        Args:
            message: Success message
            data: Optional response data

        Returns:
            BaseResponse with status=True, message, and data fields

        Example:
            >>> BaseResponse.success("User created successfully", user_data)
        """

        return cls(status=True, message=message, data=data, error=None, error_code=None)

    @classmethod
    def error(
        cls,
        message: str,
        error_code: Optional[str] = None,
        data: Optional[Any] = None
    ) -> "BaseResponse[None]":
        """
        Create an error response with backward compatibility.

        Args:
            message: Error message (maps to 'error' field in new structure)
            error_code: Optional error code (auto-inferred if not provided)
            data: DEPRECATED - for backward compatibility only

        Returns:
            BaseResponse with status=False, error, and error_code fields

        Standard error codes:
            - "VALIDATION_ERROR": Input validation failures (422)
            - "NOT_FOUND": Resource not found errors (404)
            - "AUTHENTICATION_ERROR": Authentication failures (401)
            - "AUTHORIZATION_ERROR": Permission/access errors (403)
            - "CONFLICT_ERROR": Resource conflicts (409)
            - "HTTP_ERROR": Default fallback for generic errors (500)

        Examples:
            >>> BaseResponse.error("Authentication failed", "AUTHENTICATION_ERROR")
            >>> BaseResponse.error("User not found", "NOT_FOUND")
            >>> BaseResponse.error("Authentication failed")  # auto-infers AUTHENTICATION_ERROR
        """
        # Determine error code
        if error_code is None:
            if data is not None:
                actual_error_code = cls._infer_error_code_from_data(data, message)
            else:
                actual_error_code = cls._infer_error_code_from_message(message)
        else:
            actual_error_code = error_code

        from typing import cast
        return cast("BaseResponse[None]", cls(status=False, message=None, data=None, error=message, error_code=actual_error_code))

    @classmethod
    def _infer_error_code_from_message(cls, message: str) -> str:
        """Infer error code from error message for backward compatibility."""
        message_lower = message.lower()

        if any(word in message_lower for word in ["not found", "does not exist", "missing"]):
            return "NOT_FOUND"
        elif any(word in message_lower for word in ["authentication", "login", "credentials", "token", "unauthorized"]):
            return "AUTHENTICATION_ERROR"
        elif any(word in message_lower for word in ["permission", "access", "forbidden", "not allowed"]):
            return "AUTHORIZATION_ERROR"
        elif any(word in message_lower for word in ["validation", "invalid", "format", "required"]):
            return "VALIDATION_ERROR"
        elif any(word in message_lower for word in ["conflict", "already exists", "duplicate"]):
            return "CONFLICT_ERROR"
        else:
            return "HTTP_ERROR"

    @classmethod
    def _infer_error_code_from_data(cls, data: Any, message: str) -> str:
        """Infer error code from data context for backward compatibility."""
        # First try to infer from message
        code_from_message = cls._infer_error_code_from_message(message)
        if code_from_message != "HTTP_ERROR":
            return code_from_message

        # Then try to infer from data structure
        if isinstance(data, dict):
            error_info = str(data).lower()
            if "not found" in error_info or "missing" in error_info:
                return "NOT_FOUND"
            elif "authentication" in error_info or "credentials" in error_info:
                return "AUTHENTICATION_ERROR"
            elif "validation" in error_info or "invalid" in error_info:
                return "VALIDATION_ERROR"
            elif "conflict" in error_info or "exists" in error_info:
                return "CONFLICT_ERROR"

        return "HTTP_ERROR"

    @classmethod
    def from_data(
        cls,
        data: DataType,
        message: Optional[str] = None
    ) -> "BaseResponse[DataType]":
        """
        Create a success response from data with optional custom message.

        Args:
            data: Response data
            message: Optional custom message (defaults to "Operation successful")

        Returns:
            BaseResponse with status=True and provided data

        Example:
            >>> BaseResponse.from_data(user_list, "Users retrieved successfully")
        """
        default_message = "Operation successful"
        return cls(status=True, message=message or default_message, data=data, error=None, error_code=None)


# Type aliases for common response patterns
AnyResponse = BaseResponse[Any]
DictResponse = BaseResponse[Dict[str, Any]]
ListResponse = BaseResponse[List[Any]]
StringResponse = BaseResponse[str]
IntResponse = BaseResponse[int]
BoolResponse = BaseResponse[bool]


class HealthCheck(BaseSchema):
    """Health check response schema."""
    
    status: str
    timestamp: datetime
    version: str
    services: Dict[str, str]  # service_name -> status
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "healthy",
                "timestamp": "2024-01-01T00:00:00Z",
                "version": "1.0.0",
                "services": {
                    "database": "healthy",
                    "redis": "healthy",
                    "mongodb": "healthy",
                    "rabbitmq": "healthy"
                }
            }
        }
    )
