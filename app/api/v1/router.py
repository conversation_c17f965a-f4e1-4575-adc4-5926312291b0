"""
Main API router for version 1 endpoints.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, credential, health, node, users

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    health.router,
    tags=["health"]
)

api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

api_router.include_router(
    node.router,
    prefix="/nodes",
    tags=["nodes"]
)

api_router.include_router(
    credential.router,
    prefix="/credentials",
    tags=["credentials"]
)
