from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.http_request.http_request_model import HTTPRequestNodeDescription
import json



@node_defn(type='http_request', is_activity=True)
class HTTPRequestNode(Node):
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        return HTTPRequestNodeDescription.create()
      
    async def run(self, data: NodeData) -> NodeResult:
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid data")

        # Extract parameters
        url = self.data.parameters.get('url')
        if not url:
            return NodeResult(error="URL is required")
        url = str(url)
            
        method = self.data.parameters.get('method', 'GET')
        method = str(method).upper()
        
        # Handle headers conversion - consolidated logic
        headers = self.data.parameters.get('headers', {})
        if headers is None or headers == {}:
            headers = None
        elif isinstance(headers, str):
            try:
                headers = json.loads(headers)
            except json.JSONDecodeError:
                return NodeResult(error="Invalid JSON format for headers")
        elif not isinstance(headers, dict):
            # Convert other types to empty dict
            headers = {}
        
        # Handle payload conversion - consolidated logic
        payload = self.data.parameters.get('payload', {})
        if payload is None or payload == {}:
            payload = None
        elif isinstance(payload, str):
            try:
                payload = json.loads(payload)
            except json.JSONDecodeError:
                return NodeResult(error="Invalid JSON format for payload")
        elif not isinstance(payload, dict):
            # Convert other types to empty dict
            payload = {}
        
        # Make the request
        try:
            from app.utils.http_client import HttpClient, HttpClientError
            response = await HttpClient.request(
                method=method,
                url=url,
                headers=headers,
                data=payload if method in ['POST', 'PUT', 'PATCH'] else None,
                params=payload if method in ['GET', 'DELETE'] else None,
                timeout=30  # Set a reasonable timeout
            )
            
            # Prepare response data
            result = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text
            }
            
            # Try to parse JSON if possible
            try:
                result["json"] = response["json"]
            except:
                pass  # Not JSON content, that's fine
                
            return NodeResult(
                result=result,
                next_connection_index=0
            )
            
        except HttpClientError as e:
            return NodeResult(error=f"Request failed: {str(e)}")
            
        except Exception as e:
            return NodeResult(error=f"Webhook execution error: {str(e)}")
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        return self.base_validate(request)
