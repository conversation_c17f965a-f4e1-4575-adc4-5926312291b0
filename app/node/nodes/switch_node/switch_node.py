from app.node.node_base.node import Node, Node<PERSON><PERSON>ult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult, ValidationError
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.switch_node.switch_model import SwitchNodeDescription
import json
import re
from typing import Any, Dict, List, Union


@node_defn(type='switch', is_activity=False)
class SwitchNode(Node):
    """
    Switch Node for multi-way workflow routing.
    
    This node evaluates conditions or expressions and routes execution to one of multiple outputs
    based on the evaluation result. Supports both rules-based routing and expression-based routing.
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return SwitchNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the Switch node's routing logic.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with next_connection_index set to the appropriate output index
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")

        try:
            # Get routing mode
            mode = str(self.data.parameters.get('mode', 'rules'))
            
            # Route based on mode
            if mode == 'expression':
                output_index = await self._evaluate_expression_routing()
            elif mode == 'rules':
                output_index = await self._evaluate_rules_routing()
            else:
                return NodeResult(error=f"Unknown routing mode: {mode}")
            
            # Validate output index
            if output_index < 0:
                # No output selected (e.g., no rules matched and no fallback)
                return NodeResult(
                    result={"routed": False, "output_index": None},
                    next_connection_index=None  # No output
                )
            
            return NodeResult(
                result={"routed": True, "output_index": output_index},
                next_connection_index=output_index
            )
            
        except Exception as e:
            return NodeResult(error=f"Switch routing error: {str(e)}")
    
    async def _evaluate_expression_routing(self) -> int:
        """Evaluate expression-based routing."""
        expression = str(self.data.parameters.get('output_expression', '0'))
        number_outputs = int(self.data.parameters.get('number_outputs', 4))
        
        if not expression:
            return 0
        
        try:
            # Process the expression for basic evaluation
            processed_expr = self._process_expression(expression)
            
            # Use eval with restricted globals for basic expressions
            allowed_names = {
                "__builtins__": {},
                "True": True,
                "False": False,
                "None": None,
                "len": len,
                "str": str,
                "int": int,
                "float": float,
                "bool": bool,
                "abs": abs,
                "min": min,
                "max": max
            }
            
            result = eval(processed_expr, allowed_names)
            output_index = int(result)
            
            # Validate output index range
            if output_index < 0 or output_index >= number_outputs:
                raise ValueError(f"Output index {output_index} is out of range (0-{number_outputs-1})")
            
            return output_index
            
        except Exception as e:
            raise ValueError(f"Expression evaluation failed: {str(e)}")
    
    async def _evaluate_rules_routing(self) -> int:
        """Evaluate rules-based routing."""
        # For simplified implementation, get rule parameters directly
        value1 = self.data.parameters.get('rule_value1', '')
        value2 = self.data.parameters.get('rule_value2', '')
        operation = str(self.data.parameters.get('rule_operation', 'equal'))
        output_index = int(self.data.parameters.get('rule_output_index', 0))
        fallback_output = self.data.parameters.get('fallback_output', -1)

        # Get options
        case_sensitive = bool(self.data.parameters.get('case_sensitive', True))
        ignore_whitespace = bool(self.data.parameters.get('ignore_whitespace', False))

        # Process values
        val1 = self._process_value(value1, case_sensitive, ignore_whitespace)
        val2 = self._process_value(value2, case_sensitive, ignore_whitespace)

        # Evaluate condition
        if self._compare_values(val1, val2, operation):
            return output_index

        # No match found, use fallback
        if fallback_output >= 0:
            return int(fallback_output)

        # No fallback, return -1 to indicate no output
        return -1
    
    def _process_value(self, value: Any, case_sensitive: bool = True, ignore_whitespace: bool = False) -> Any:
        """Process a value according to options."""
        if isinstance(value, str):
            if ignore_whitespace:
                value = value.strip()
            if not case_sensitive:
                value = value.lower()
        
        return value
    
    def _compare_values(self, val1: Any, val2: Any, operation: str) -> bool:
        """Compare two values using the specified operation."""
        try:
            if operation == 'equal':
                return val1 == val2
            elif operation == 'notEqual':
                return val1 != val2
            elif operation == 'greaterThan':
                return self._to_number(val1) > self._to_number(val2)
            elif operation == 'lessThan':
                return self._to_number(val1) < self._to_number(val2)
            elif operation == 'greaterThanOrEqual':
                return self._to_number(val1) >= self._to_number(val2)
            elif operation == 'lessThanOrEqual':
                return self._to_number(val1) <= self._to_number(val2)
            elif operation == 'contains':
                return str(val2) in str(val1)
            elif operation == 'notContains':
                return str(val2) not in str(val1)
            elif operation == 'startsWith':
                return str(val1).startswith(str(val2))
            elif operation == 'endsWith':
                return str(val1).endswith(str(val2))
            elif operation == 'isEmpty':
                return not val1 or (isinstance(val1, str) and val1.strip() == '')
            elif operation == 'isNotEmpty':
                return bool(val1) and not (isinstance(val1, str) and val1.strip() == '')
            elif operation == 'regex':
                return bool(re.search(str(val2), str(val1)))
            else:
                raise ValueError(f"Unknown operation: {operation}")
                
        except Exception as e:
            raise ValueError(f"Comparison failed for operation '{operation}': {str(e)}")
    
    def _to_number(self, value: Any) -> Union[int, float]:
        """Convert value to number for numeric comparisons."""
        if isinstance(value, (int, float)):
            return value
        
        try:
            # Try int first
            if isinstance(value, str) and '.' not in value:
                return int(value)
            else:
                return float(value)
        except (ValueError, TypeError):
            raise ValueError(f"Cannot convert '{value}' to number")
    
    def _process_expression(self, expression: str) -> str:
        """Process expression for basic evaluation."""
        # Simple preprocessing - replace common patterns
        processed = expression
        
        # Replace common JavaScript-like operators with Python equivalents
        processed = processed.replace('&&', ' and ')
        processed = processed.replace('||', ' or ')
        processed = processed.replace('!', ' not ')
        processed = processed.replace('===', '==')
        processed = processed.replace('!==', '!=')
        
        return processed
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """Validate the Switch node request."""
        # Perform base validation
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result
        
        # Add specific validation for Switch node
        errors = []
        
        if not request.parameters:
            return ValidationResult(valid=False, errors=[ValidationError(parameter="", message="Parameters are required")])
        
        mode = request.parameters.get('mode', 'rules')
        
        if mode == 'expression':
            if not request.parameters.get('output_expression'):
                errors.append(ValidationError(parameter="output_expression", message="Output expression is required for expression mode"))
            
            number_outputs = request.parameters.get('number_outputs')
            if number_outputs is not None:
                try:
                    num_outputs = int(number_outputs)
                    if num_outputs < 1 or num_outputs > 20:
                        errors.append(ValidationError(parameter="number_outputs", message="Number of outputs must be between 1 and 20"))
                except (ValueError, TypeError):
                    errors.append(ValidationError(parameter="number_outputs", message="Number of outputs must be a valid number"))
        
        elif mode == 'rules':
            rule_value1 = request.parameters.get('rule_value1')
            if not rule_value1:
                errors.append(ValidationError(parameter="rule_value1", message="Rule value 1 is required for rules mode"))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)
