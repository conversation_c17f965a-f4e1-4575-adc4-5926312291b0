# Cerebro - Modern FastAPI Application

A production-ready FastAPI application with clean architecture, comprehensive testing, and modern development practices.

## 🚀 Features

- **Modern FastAPI**: Built with FastAPI 0.115+ for high performance and automatic API documentation
- **Clean Architecture**: Layered architecture with clear separation of concerns
- **Async/Await**: Full async support with SQLAlchemy 2.0 and asyncpg
- **Authentication & Authorization**: JWT-based auth with role-based access control (RBAC)
- **Database Support**: PostgreSQL with Alembic migrations, MongoDB with Motor
- **Caching**: Redis integration for high-performance caching
- **Message Queue**: RabbitMQ with aio-pika for async message processing
- **Event Processing**: Inngest integration for reliable background jobs
- **Comprehensive Testing**: Unit, integration, and API tests with pytest
- **Code Quality**: Pre-commit hooks, linting, formatting, and type checking
- **Docker Support**: Multi-service Docker Compose setup
- **Monitoring**: Health checks and structured logging

## 🏗️ Architecture

```
cerebro/
├── app/
│   ├── api/                    # API layer (FastAPI routes)
│   ├── core/                   # Core configuration and utilities
│   ├── models/                 # SQLAlchemy models
│   ├── schemas/                # Pydantic schemas
│   ├── services/               # Business logic layer
│   ├── repositories/           # Data access layer
│   └── utils/                  # Utility functions
├── tests/                      # Comprehensive test suite
├── alembic/                    # Database migrations
└── docker-compose.yml         # Multi-service setup
```

## 🛠️ Technology Stack

- **Framework**: FastAPI 0.115+
- **Database**: PostgreSQL with SQLAlchemy 2.0 (async)
- **Document Store**: MongoDB with Motor
- **Cache**: Redis
- **Message Queue**: RabbitMQ
- **Event Processing**: Inngest
- **Authentication**: JWT with python-jose
- **Password Hashing**: bcrypt via passlib
- **Validation**: Pydantic v2
- **Testing**: pytest with async support
- **Code Quality**: Black, isort, flake8, mypy
- **Containerization**: Docker & Docker Compose

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Git

### 1. Clone the Repository

```bash
git clone <repository-url>
cd cerebro
```

### 2. Environment Setup

```bash
# Copy environment file
cp .env.example .env

# Edit .env file with your configuration
# Update database credentials, secret keys, etc.
```

### 3. Start Services with Docker

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app
```

### 4. Run Database Migrations

```bash
# Run migrations
docker-compose exec app alembic upgrade head
```

### 5. Access the Application

- **API Documentation**: http://localhost:8000/api/v1/docs
- **Alternative Docs**: http://localhost:8000/api/v1/redoc
- **Health Check**: http://localhost:8000/api/v1/health
- **RabbitMQ Management**: http://localhost:15672 (guest/guest)

## 🔧 Development Setup

### Local Development

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run the application
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Database Setup

```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Downgrade migration
alembic downgrade -1
```

## 🧪 Testing

### Run All Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test types
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m api          # API tests only
```

### Test Categories

- **Unit Tests**: Fast, isolated tests for individual components
- **Integration Tests**: Database and service integration tests
- **API Tests**: End-to-end API endpoint tests

## 📊 Code Quality

### Linting and Formatting

```bash
# Format code
black app tests
isort app tests

# Lint code
flake8 app tests
mypy app

# Run all quality checks
pre-commit run --all-files
```

### Pre-commit Hooks

The project uses pre-commit hooks to ensure code quality:

- **black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **bandit**: Security linting

## 🔐 Authentication & Authorization

### User Roles

- **Owner**: Full system access
- **Admin**: Management access
- **Editor**: Read and write access
- **Viewer**: Read-only access (default)

### API Authentication

```bash
# Register a new user
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "full_name": "Test User",
    "password": "SecurePass123!"
  }'

# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# Use the token
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 🐳 Docker Services

The application includes the following services:

- **app**: FastAPI application
- **postgres**: PostgreSQL database
- **mongodb**: MongoDB document store
- **redis**: Redis cache
- **rabbitmq**: RabbitMQ message broker
- **inngest**: Inngest event processing

### Service URLs

- **PostgreSQL**: localhost:5432
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379
- **RabbitMQ**: localhost:5672 (AMQP), localhost:15672 (Management)
- **Inngest**: localhost:8288

## 📝 API Documentation

### Key Endpoints

- `GET /api/v1/health` - Health check
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Current user profile
- `GET /api/v1/users/` - List users (paginated)
- `POST /api/v1/users/` - Create user
- `GET /api/v1/users/{id}` - Get user by ID
- `PUT /api/v1/users/{id}` - Update user
- `DELETE /api/v1/users/{id}` - Delete user

### Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": {...},
  "message": "Operation successful"
}
```

Error responses:

```json
{
  "success": false,
  "error": "Error message",
  "error_code": "ERROR_CODE",
  "details": {...}
}
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Application
APP_NAME=Cerebro API
DEBUG=false
SECRET_KEY=your-secret-key

# Database
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/cerebro
MONGODB_URL=*******************************************
REDIS_URL=redis://:password@localhost:6379/0

# Authentication
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080

# External Services
RABBITMQ_URL=amqp://user:pass@localhost:5672/cerebro
INNGEST_URL=http://localhost:8288
```

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**:
   ```bash
   cp .env.example .env.production
   # Update production values
   ```

2. **Build and Deploy**:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

3. **Run Migrations**:
   ```bash
   docker-compose exec app alembic upgrade head
   ```

### Health Monitoring

- **Liveness**: `GET /api/v1/health/live`
- **Readiness**: `GET /api/v1/health/ready`
- **Startup**: `GET /api/v1/health/startup`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `pytest`
5. Run quality checks: `pre-commit run --all-files`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` endpoint for interactive API documentation
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join the community discussions

## 🙏 Acknowledgments

- FastAPI for the excellent framework
- SQLAlchemy for powerful ORM capabilities
- Pydantic for data validation
- All the amazing open-source contributors
